import logging
import datetime
import os

class StructuredLogger:
    """
    A structured logging class that provides consistent log formatting
    with timestamp, version, source location, and message details.
    """
    
    def __init__(self, version="1.0.0", log_file=None):
        """
        Initialize the structured logger.
        
        Args:
            version (str): Application version to include in logs
            log_file (str): Path to log file, defaults to env var or 'app.log'
        """
        self.version = version
        log_file = log_file or os.getenv('log_file') or 'app.log'

        # Create log file if it doesn't exist
        if not os.path.exists(log_file):
            with open(log_file, "w") as file:
                file.write("")

        # Set up logger with both console and file output
        self.logger = logging.getLogger("StructuredLogger")
        self.logger.setLevel(logging.DEBUG)
        
        # Prevent duplicate handlers if logger already exists
        if not self.logger.hasHandlers():
            # Console handler for immediate feedback
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(logging.Formatter('%(message)s'))
            self.logger.addHandler(console_handler)

            # File handler for persistent logging
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(logging.Formatter('%(message)s'))
            self.logger.addHandler(file_handler)

    def log(self, level, message, source):
        """
        Core logging method that creates structured log entries.
        
        Args:
            level (str): Log level (INFO, WARNING, ERROR, CRITICAL, DEBUG)
            message (str): The log message
            source (str): Source location (typically filename:line_number)
        """
        # Create structured log entry with consistent format
        log_entry = {
            "level": level.upper(),
            "timestamp": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "version": self.version,
            "message": message,
            "source": source
        }
        
        # Route to appropriate logging method based on level
        if level.upper() == "INFO":
            self.logger.info(log_entry)
        elif level.upper() == "WARNING":
            self.logger.warning(log_entry)
        elif level.upper() == "ERROR":
            self.logger.error(log_entry)
        elif level.upper() == "CRITICAL":
            self.logger.critical(log_entry)
        elif level.upper() == "DEBUG":
            self.logger.debug(log_entry)
        else:
            raise ValueError(f"Unsupported log level: {level}")

    def info(self, message, source):
        """Log informational message."""
        self.log("INFO", message, source)

    def warning(self, message, source):
        """Log warning message."""
        self.log("WARNING", message, source)

    def error(self, message, source):
        """Log error message."""
        self.log("ERROR", message, source)

    def critical(self, message, source):
        """Log critical error message."""
        self.log("CRITICAL", message, source)

    def debug(self, message, source):
        """Log debug message for troubleshooting."""
        self.log("DEBUG", message, source)
