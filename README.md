# Python Utilities Collection

A collection of Python utility modules for encryption, database management, and structured logging.

## Overview

This repository contains three main utility modules:

- **`encryption.py`** - Secure encryption/decryption utilities with AES-256 support
- **`db_manager.py`** - Unified database manager for MSSQL and SQLite operations
- **`logger_instance.py`** - Structured logging with consistent formatting

## Requirements

### Dependencies

```bash
pip install cryptography sqlalchemy pandas python-dotenv
```

### System Requirements

- Python 3.7+
- For MSSQL: ODBC Driver 17 for SQL Server
- For SQLite: Built-in sqlite3 module

## Module Documentation

### 1. encryption.py

Provides secure encryption capabilities with multiple classes for different use cases.

#### SecureEncryption (Recommended)

Production-ready AES-256-CBC encryption with random IV generation.

```python
from encryption import SecureEncryption

# Generate new key
encryptor = SecureEncryption()
print(f"Generated key: {encryptor.get_key_hex()}")

# Use existing key
encryptor = SecureEncryption("my_secret_password")

# Encrypt/decrypt text
message = "This is a secret message!"
encrypted = encryptor.encrypt(message)
decrypted = encryptor.decrypt(encrypted)

# File encryption
encryptor.encrypt_file('document.txt', 'document.txt.enc')
encryptor.decrypt_file('document.txt.enc', 'document_decrypted.txt')

# Key management
hex_key = encryptor.get_key_hex()
new_encryptor = SecureEncryption.from_hex_key(hex_key)
```

#### LegacyEncryption / AES256

Backward compatibility class for existing implementations.

```python
from encryption import AES256

key = SecureEncryption.generate_key_string()
encrypted = AES256(key).encrypt(message)
decrypted = AES256(key).decrypt(encrypted)
```

#### Encryption (Base64 Encoding)

Simple Base64 encoding/decoding for non-sensitive data obfuscation.

```python
from encryption import Encryption

encoded = Encryption.encode('Hello World')
decoded = Encryption.decode(encoded)
```

**Security Notes:**
- Use `SecureEncryption` for new implementations
- `SecureEncryption` uses random IV for each encryption (secure)
- `LegacyEncryption/AES256` uses static IV (backward compatibility only)
- Base64 encoding is NOT encryption - use `SecureEncryption` for sensitive data

### 2. db_manager.py

Unified database manager supporting both MSSQL and SQLite with consistent interface.

#### Basic Usage

```python
from db_manager import DatabaseManager

# MSSQL connection (default)
db_mssql = DatabaseManager()

# SQLite connection
db_sqlite = DatabaseManager(db_type="sqlite", sqlite_path="my_database.db")
```

#### Environment Variables

Create a `.env` file with your database configuration:

```env
# MSSQL Configuration
MSSQL_HOST=**********
MSSQL_PORT=1433
MSSQL_DB=overwatch
MSSQL_USER=sa
MSSQL_PASS=P@ssw0rd

# SQLite Configuration
SQLITE_PATH=database.db

# Application Configuration
app_ver=1.0.0
log_file=app.log
```

#### Database Operations

```python
# SELECT operations
df_users = db.execute("select", "users")
df_filtered = db.execute("select", "users", where_clause="user_id = '123'")

# INSERT operations
db.execute("insert", "users", {"user_name": "John", "user_email": "<EMAIL>"})

# UPDATE operations
db.execute("update", "users", {"user_status": "inactive"}, "user_id = '123'")

# DELETE operations
db.execute("delete", "users", where_clause="user_id = '123'")

# Stored procedures (MSSQL only)
result_df = db.execute_procedure("GetUsersByRole", {"role_id": "admin"})
db.call_procedure("RefreshUserCache")

# Clean up
db.close()
```

#### Features

- **Dual Database Support**: MSSQL and SQLite with unified interface
- **Connection Resilience**: Network connectivity testing and graceful failure handling
- **Security**: Parameterized queries to prevent SQL injection
- **Connection Pooling**: Automatic connection management and recycling
- **Error Handling**: Comprehensive error logging and graceful degradation

### 3. logger_instance.py

Structured logging with consistent formatting across applications.

#### Basic Usage

```python
from logger_instance import StructuredLogger
import inspect

# Initialize logger
logger = StructuredLogger(version="1.0.0", log_file="app.log")

# Get source location for logging
frame = inspect.stack()[0]
source = f"{frame.filename}:{frame.lineno}"

# Log messages at different levels
logger.info("Application started successfully", source)
logger.warning("Configuration file not found, using defaults", source)
logger.error("Database connection failed", source)
logger.critical("System out of memory", source)
logger.debug("Processing user request", source)
```

#### Log Format

Each log entry includes:
- **Level**: Log severity (INFO, WARNING, ERROR, CRITICAL, DEBUG)
- **Timestamp**: ISO format timestamp
- **Version**: Application version
- **Message**: Log message content
- **Source**: Source file and line number

#### Features

- **Dual Output**: Console and file logging
- **Structured Format**: Consistent log entry structure
- **Version Tracking**: Application version in each log entry
- **Source Tracking**: File and line number identification
- **Configurable**: Environment variable support for log file path

## Environment Configuration

Create a `.env` file in your project root:

```env
# Database Configuration
MSSQL_HOST=your_server
MSSQL_PORT=1433
MSSQL_DB=your_database
MSSQL_USER=your_username
MSSQL_PASS=your_password
SQLITE_PATH=database.db

# Application Configuration
app_ver=1.0.0
log_file=application.log
```

## Error Handling

All modules implement comprehensive error handling:

- **Graceful Degradation**: Continue operation when possible
- **Detailed Logging**: Structured error messages with context
- **Network Resilience**: Connection testing and timeout handling
- **Resource Cleanup**: Proper connection and resource management

## Security Considerations

- Store encryption keys securely (environment variables, key management systems)
- Use HTTPS when transmitting encrypted data
- Use parameterized queries to prevent SQL injection
- Enable database connection encryption
- Regularly rotate encryption keys and database credentials

## License

This project is provided as-is for utility purposes. Please review and test thoroughly before using in production environments.
