#!/usr/bin/env python3

import base64
import hashlib
import os
import secrets
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.backends import default_backend

class SecureEncryption:
    """
    Secure encryption/decryption class using AES-256-CBC with proper random IV generation.
    This class provides production-ready encryption with security best practices.
    """
    
    def __init__(self, key=None):
        """
        Initialize the encryption class with a key.
        
        Args:
            key (str or bytes): Encryption key. If None, generates a new random key.
        """
        if key is None:
            # Generate a new random 256-bit key
            self.key = self.generate_key()
        elif isinstance(key, str):
            # Hash string key to ensure proper length
            self.key = hashlib.sha256(key.encode()).digest()
        elif isinstance(key, bytes):
            # Use bytes key directly if it's the right length
            if len(key) == 32:
                self.key = key
            else:
                # Hash if wrong length
                self.key = hashlib.sha256(key).digest()
        else:
            raise ValueError("Key must be string, bytes, or None")
    
    @staticmethod
    def generate_key():
        """
        Generate a cryptographically secure random 256-bit key.
        
        Returns:
            bytes: 32-byte random key suitable for AES-256
        """
        return secrets.token_bytes(32)
    
    @staticmethod
    def generate_key_string():
        """
        Generate a random key as a hex string for easy storage/transmission.
        
        Returns:
            str: 64-character hex string representing a 256-bit key
        """
        return secrets.token_hex(32)
    
    def encrypt(self, plaintext):
        """
        Encrypt plaintext using AES-256-CBC with random IV.
        
        Args:
            plaintext (str): Text to encrypt
            
        Returns:
            str: Base64-encoded string containing IV + encrypted data
            
        Format: Base64(IV + encrypted_data)
        """
        if not isinstance(plaintext, str):
            raise ValueError("Plaintext must be a string")
        
        # Convert string to bytes
        data = plaintext.encode('utf-8')
        
        # Generate random IV for this encryption operation
        iv = os.urandom(16)  # 128-bit IV for AES
        
        # Create cipher with AES-256-CBC
        cipher = Cipher(
            algorithms.AES(self.key),
            modes.CBC(iv),
            backend=default_backend()
        )
        
        # Apply PKCS7 padding
        padder = padding.PKCS7(128).padder()
        padded_data = padder.update(data)
        padded_data += padder.finalize()
        
        # Encrypt the data
        encryptor = cipher.encryptor()
        ciphertext = encryptor.update(padded_data) + encryptor.finalize()
        
        # Combine IV and ciphertext, then encode as Base64
        encrypted_data = iv + ciphertext
        return base64.b64encode(encrypted_data).decode('utf-8')
    
    def decrypt(self, encrypted_data):
        """
        Decrypt Base64-encoded encrypted data.
        
        Args:
            encrypted_data (str): Base64-encoded encrypted data with IV
            
        Returns:
            str: Decrypted plaintext
            
        Raises:
            ValueError: If data is invalid or decryption fails
        """
        if not isinstance(encrypted_data, str):
            raise ValueError("Encrypted data must be a string")
        
        try:
            # Decode Base64 to get raw encrypted data
            raw_data = base64.b64decode(encrypted_data)
            
            # Extract IV (first 16 bytes) and ciphertext (rest)
            if len(raw_data) < 16:
                raise ValueError("Invalid encrypted data: too short")
            
            iv = raw_data[:16]
            ciphertext = raw_data[16:]
            
            # Create cipher with extracted IV
            cipher = Cipher(
                algorithms.AES(self.key),
                modes.CBC(iv),
                backend=default_backend()
            )
            
            # Decrypt the data
            decryptor = cipher.decryptor()
            padded_plaintext = decryptor.update(ciphertext) + decryptor.finalize()
            
            # Remove PKCS7 padding
            unpadder = padding.PKCS7(128).unpadder()
            plaintext = unpadder.update(padded_plaintext)
            plaintext += unpadder.finalize()
            
            # Convert bytes back to string
            return plaintext.decode('utf-8')
            
        except Exception as e:
            raise ValueError(f"Decryption failed: {str(e)}")
    
    def encrypt_file(self, input_file_path, output_file_path):
        """
        Encrypt a file and save the result to another file.
        
        Args:
            input_file_path (str): Path to file to encrypt
            output_file_path (str): Path where encrypted file will be saved
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Read the input file
            with open(input_file_path, 'rb') as f:
                file_data = f.read()
            
            # Generate random IV
            iv = os.urandom(16)
            
            # Create cipher
            cipher = Cipher(
                algorithms.AES(self.key),
                modes.CBC(iv),
                backend=default_backend()
            )
            
            # Apply padding
            padder = padding.PKCS7(128).padder()
            padded_data = padder.update(file_data)
            padded_data += padder.finalize()
            
            # Encrypt
            encryptor = cipher.encryptor()
            ciphertext = encryptor.update(padded_data) + encryptor.finalize()
            
            # Write IV + encrypted data to output file
            with open(output_file_path, 'wb') as f:
                f.write(iv + ciphertext)
            
            return True
            
        except Exception as e:
            print(f"File encryption failed: {e}")
            return False
    
    def decrypt_file(self, input_file_path, output_file_path):
        """
        Decrypt a file and save the result to another file.
        
        Args:
            input_file_path (str): Path to encrypted file
            output_file_path (str): Path where decrypted file will be saved
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Read the encrypted file
            with open(input_file_path, 'rb') as f:
                encrypted_data = f.read()
            
            # Extract IV and ciphertext
            if len(encrypted_data) < 16:
                raise ValueError("Invalid encrypted file: too short")
            
            iv = encrypted_data[:16]
            ciphertext = encrypted_data[16:]
            
            # Create cipher
            cipher = Cipher(
                algorithms.AES(self.key),
                modes.CBC(iv),
                backend=default_backend()
            )
            
            # Decrypt
            decryptor = cipher.decryptor()
            padded_plaintext = decryptor.update(ciphertext) + decryptor.finalize()
            
            # Remove padding
            unpadder = padding.PKCS7(128).unpadder()
            plaintext = unpadder.update(padded_plaintext)
            plaintext += unpadder.finalize()
            
            # Write decrypted data to output file
            with open(output_file_path, 'wb') as f:
                f.write(plaintext)
            
            return True
            
        except Exception as e:
            print(f"File decryption failed: {e}")
            return False
    
    def get_key_hex(self):
        """
        Get the current key as a hex string for storage/transmission.
        
        Returns:
            str: Hex representation of the key
        """
        return self.key.hex()
    
    @classmethod
    def from_hex_key(cls, hex_key):
        """
        Create SecureEncryption instance from a hex key string.
        
        Args:
            hex_key (str): Hex string representation of the key
            
        Returns:
            SecureEncryption: New instance with the specified key
        """
        try:
            key_bytes = bytes.fromhex(hex_key)
            return cls(key_bytes)
        except ValueError:
            raise ValueError("Invalid hex key format")

class LegacyEncryption:
    """
    Legacy encryption class for backward compatibility.
    Maintains the original AES256 and Encryption class functionality.
    """
    
    def __init__(self, key):
        """Initialize with legacy key handling."""
        self.key = hashlib.sha256(key.encode()).digest()
    
    def encrypt(self, message):
        """Legacy encrypt method using static IV (not secure)."""
        # This maintains backward compatibility but is not secure
        from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
        from cryptography.hazmat.primitives import padding
        
        # Static IV for backward compatibility (NOT SECURE)
        iv = b'\x00' * 16
        
        data = message.encode('utf-8')
        
        cipher = Cipher(algorithms.AES(self.key), modes.CBC(iv), backend=default_backend())
        
        padder = padding.PKCS7(128).padder()
        padded_data = padder.update(data)
        padded_data += padder.finalize()
        
        encryptor = cipher.encryptor()
        ciphertext = encryptor.update(padded_data) + encryptor.finalize()
        
        return base64.b64encode(ciphertext).decode('utf-8')
    
    def decrypt(self, encrypted_data):
        """Legacy decrypt method using static IV."""
        from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
        from cryptography.hazmat.primitives import padding
        
        # Static IV for backward compatibility
        iv = b'\x00' * 16
        
        ciphertext = base64.b64decode(encrypted_data)
        
        cipher = Cipher(algorithms.AES(self.key), modes.CBC(iv), backend=default_backend())
        
        decryptor = cipher.decryptor()
        padded_plaintext = decryptor.update(ciphertext) + decryptor.finalize()
        
        unpadder = padding.PKCS7(128).unpadder()
        plaintext = unpadder.update(padded_plaintext)
        plaintext += unpadder.finalize()
        
        return plaintext.decode('utf-8')

# Maintain backward compatibility
AES256 = LegacyEncryption

class Encryption():
    """
    Simple Base64 encoding/decoding utility class.
    Provides basic text encoding for non-sensitive data obfuscation.
    
    Note: Base64 is NOT encryption - it's just encoding.
    Use SecureEncryption class for actual encryption needs.
    """
    
    @staticmethod
    def encode(value):
        """
        Encode a string to Base64.
        
        Args:
            value (str): String to encode
            
        Returns:
            str: Base64-encoded string
        """
        string_bytes = value.encode("utf-8")
        base64_bytes = base64.b64encode(string_bytes)
        return base64_bytes.decode("utf-8")

    @staticmethod
    def decode(value):
        """
        Decode a Base64-encoded string.
        
        Args:
            value (str): Base64-encoded string to decode
            
        Returns:
            str: Original decoded string
        """
        base64_bytes = value.encode("utf-8")
        string_bytes = base64.b64decode(base64_bytes)
        return string_bytes.decode("utf-8")


## Usage Examples:
##
## Secure Encryption (Recommended):
## --------------------------------
## # Generate a new key
## encryptor = SecureEncryption()
## print(f"Generated key: {encryptor.get_key_hex()}")
##
## # Or use existing key
## encryptor = SecureEncryption("my_secret_password")
##
## # Encrypt a message
## message = 'This is a secret message!'
## encrypted = encryptor.encrypt(message)
## print(f"Encrypted: {encrypted}")
##
## # Decrypt the message
## decrypted = encryptor.decrypt(encrypted)
## print(f"Decrypted: {decrypted}")
##
## # File encryption
## encryptor.encrypt_file('document.txt', 'document.txt.enc')
## encryptor.decrypt_file('document.txt.enc', 'document_decrypted.txt')
##
## # Key management
## hex_key = encryptor.get_key_hex()
## new_encryptor = SecureEncryption.from_hex_key(hex_key)
##
## Legacy AES-256 (Backward Compatibility):
## ----------------------------------------
## # Generate a secure key
## key = SecureEncryption.generate_key_string()
## encrypted = AES256(key).encrypt(message)
## decrypted = AES256(key).decrypt(encrypted)
##
## Base64 Encoding:
## ---------------
## encoded = Encryption.encode('Hello World')
## decoded = Encryption.decode(encoded)
##
## Security Notes:
## --------------
## 1. Use SecureEncryption for new implementations
## 2. SecureEncryption uses random IV for each encryption (secure)
## 3. LegacyEncryption/AES256 uses static IV (backward compatibility only)
## 4. Store encryption keys securely (environment variables, key management systems)
## 5. Base64 encoding is NOT encryption - use SecureEncryption for sensitive data
## 6. Always use HTTPS when transmitting encrypted data
