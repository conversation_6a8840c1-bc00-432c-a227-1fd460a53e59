#!/usr/bin/env python3

from sqlalchemy import create_engine, text
import pandas as pd
import os, inspect
from sqlalchemy.engine import URL
import urllib.parse
from dotenv import load_dotenv
from logger_instance import StructuredLogger

# Load environment variables from .env file
load_dotenv()

# Initialize logger with application version from environment
logger = StructuredLogger(version=os.getenv('app_ver', '0.0.0'))

class DatabaseManager:
    """
    A unified database manager that supports both MSSQL and SQLite connections.
    Provides a consistent interface for database operations across different database types.
    """
    
    def __init__(self, db_type="mssql", sqlite_path=None):
        """
        Initialize the database manager with specified database type.
        
        Args:
            db_type (str): Database type - either "mssql" or "sqlite"
            sqlite_path (str): Path to SQLite database file (only used for SQLite)
        """
        self.db_type = db_type.lower()
        self.engine = None      # SQLAlchemy engine for MSSQL
        self.sqlite_conn = None # Direct SQLite connection
        
        # Initialize connection based on database type
        if self.db_type == "mssql":
            self._connect_mssql()
        elif self.db_type == "sqlite":
            self._connect_sqlite(sqlite_path)
        else:
            raise ValueError("db_type must be 'mssql' or 'sqlite'")
    
    def _connect_mssql(self):
        """
        Establish connection to Microsoft SQL Server using SQLAlchemy.
        Includes network connectivity test and comprehensive error handling.
        """
        frame = inspect.stack()[1]
        source = f"{frame.filename}:{frame.lineno}"

        # Get database connection parameters from environment variables
        server   = os.getenv("MSSQL_HOST", "**********")
        port     = os.getenv("MSSQL_PORT", "1433")
        database = os.getenv("MSSQL_DB",   "overwatch")
        username = os.getenv("MSSQL_USER", "sa")
        password = os.getenv("MSSQL_PASS", "P@ssw0rd")

        # Test network connectivity before attempting database connection
        import socket
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)  # 5 second timeout for network test
            result = sock.connect_ex((server, int(port)))
            sock.close()
            
            if result != 0:
                error_msg = f"Cannot reach SQL Server at {server}:{port} (network unreachable)"
                logger.error(error_msg, source)
                self.engine = None
                return  # Exit gracefully without raising exception
        except Exception as e:
            error_msg = f"Network connectivity test failed: {e}"
            logger.error(error_msg, source)
            self.engine = None
            return

        # Build ODBC connection string with security and timeout settings
        connection_string = (
            f"DRIVER={{ODBC Driver 17 for SQL Server}};"
            f"SERVER={server},{port};"
            f"DATABASE={database};"
            f"UID={username};"
            f"PWD={password};"
            f"Encrypt=yes;"                    # Enable encryption
            f"TrustServerCertificate=yes;"     # Trust self-signed certificates
            f"Connection Timeout=10;"          # Reduced timeout for faster failure detection
            f"Login Timeout=10;"
        )
        
        # URL encode the connection string for SQLAlchemy
        params = urllib.parse.quote_plus(connection_string)
        connection_url = URL.create("mssql+pyodbc", query={"odbc_connect": params})
        
        try:
            # Create SQLAlchemy engine with connection pooling
            self.engine = create_engine(
                connection_url, 
                echo=False,                    # Set to True for SQL debugging
                pool_pre_ping=True,           # Test connections before use
                pool_recycle=3600             # Recycle connections every hour
            )
            
            # Test the connection by executing a simple query
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            #logger.info(f"MSSQL connection established successfully", source)
            
        except Exception as e:
            # Log connection failure and continue with degraded functionality
            try:
                logger.error(f"MSSQL connection failed: {e}", source)
            except Exception as log_error:
                print(f"MSSQL connection failed: {e}")
                print(f"Logging error: {log_error}")
            self.engine = None
    
    def _connect_sqlite(self, sqlite_path):
        """
        Establish connection to SQLite database.
        
        Args:
            sqlite_path (str): Path to SQLite database file
        """
        frame = inspect.stack()[1]
        source = f"{frame.filename}:{frame.lineno}"
        
        # Use provided path or default from environment/fallback
        if not sqlite_path:
            sqlite_path = os.getenv("SQLITE_PATH", "database.db")
        
        try:
            import sqlite3
            # Create SQLite connection with thread safety disabled for performance
            self.sqlite_conn = sqlite3.connect(sqlite_path, check_same_thread=False)
            
            # Test connection with a simple query
            cursor = self.sqlite_conn.cursor()
            cursor.execute("SELECT 1")
            cursor.close()
            #logger.info(f"SQLite connection established: {sqlite_path}", source)
            
        except Exception as e:
            # Log connection failure and continue with degraded functionality
            try:
                logger.error(f"SQLite connection failed: {e}", source)
            except Exception as log_error:
                print(f"SQLite connection failed: {e}")
                print(f"Logging error: {log_error}")
            self.sqlite_conn = None
    
    def execute(self, operation="select", table=None, data=None, where_clause=None, columns="*"):
        """
        Universal database operation method that routes to appropriate database-specific handler.
        
        Args:
            operation (str): Database operation - 'select', 'insert', 'update', 'delete'
            table (str): Target table name
            data (dict): Data for insert/update operations
            where_clause (str): WHERE condition for select/update/delete operations
            columns (str): Columns to select (default: "*")
            
        Returns:
            pandas.DataFrame: For select operations
            int: Number of affected rows for insert/update/delete operations
            None: If connection failed or operation invalid
        """
        if self.db_type == "mssql":
            return self._execute_mssql(operation, table, data, where_clause, columns)
        elif self.db_type == "sqlite":
            return self._execute_sqlite(operation, table, data, where_clause, columns)
    
    def _execute_mssql(self, operation, table, data, where_clause, columns):
        """
        Execute database operations on MSSQL using SQLAlchemy.
        
        Returns:
            pandas.DataFrame: For select operations (empty DataFrame if no results)
            int: Number of affected rows for other operations
            None: If connection is unavailable
        """
        # Check if connection is available
        if self.engine is None:
            return pd.DataFrame() if operation == "select" else None

        try:
            # Use connection context manager for automatic cleanup
            with self.engine.connect() as conn:
                if operation.lower() == "select":
                    return self._select(conn, table, columns, where_clause)
                elif operation.lower() == "insert":
                    return self._insert(conn, table, data)
                elif operation.lower() == "update":
                    return self._update(conn, table, data, where_clause)
                elif operation.lower() == "delete":
                    return self._delete(conn, table, where_clause)
                else:
                    raise ValueError(f"Unsupported operation: {operation}")
                    
        except Exception as e:
            print(f"{operation.title()} failed:", e)
            return pd.DataFrame() if operation == "select" else None
    
    def _select(self, conn, table, columns, where_clause):
        """Execute SELECT query and return results as DataFrame."""
        query = f"SELECT {columns} FROM {table}"
        if where_clause:
            query += f" WHERE {where_clause}"
        
        df = pd.read_sql_query(query, conn)
        return df
    
    def _insert(self, conn, table, data):
        """Execute INSERT operation using pandas to_sql method."""
        if not data or not table:
            raise ValueError("Insert requires table and data")
        
        # Convert single record to DataFrame and insert
        df = pd.DataFrame([data])
        rows_affected = df.to_sql(table, conn, if_exists='append', index=False)
        return rows_affected
    
    def _update(self, conn, table, data, where_clause):
        """Execute UPDATE operation using parameterized query."""
        if not data or not table or not where_clause:
            raise ValueError("Update requires table, data, and where_clause")
        
        # Build parameterized SET clause for security
        set_clause = ", ".join([f"{k} = :{k}" for k in data.keys()])
        query = f"UPDATE {table} SET {set_clause} WHERE {where_clause}"
        
        result = conn.execute(text(query), data)
        conn.commit()
        return result.rowcount
    
    def _delete(self, conn, table, where_clause):
        """Execute DELETE operation with WHERE clause."""
        if not table or not where_clause:
            raise ValueError("Delete requires table and where_clause")
        
        query = f"DELETE FROM {table} WHERE {where_clause}"
        result = conn.execute(text(query))
        conn.commit()
        return result.rowcount
    
    def _execute_sqlite(self, operation, table, data, where_clause, columns):
        """
        Execute database operations on SQLite using direct connection.
        
        Returns:
            pandas.DataFrame: For select operations
            int: Number of affected rows for other operations
            None: If connection is unavailable
        """
        # Check if connection is available
        if self.sqlite_conn is None:
            return pd.DataFrame() if operation == "select" else None
        
        try:
            if operation.lower() == "select":
                # Build and execute SELECT query
                query = f"SELECT {columns} FROM {table}"
                if where_clause:
                    query += f" WHERE {where_clause}"
                return pd.read_sql_query(query, self.sqlite_conn)
            
            elif operation.lower() == "insert":
                # Insert using pandas to_sql method
                if not data or not table:
                    raise ValueError("Insert requires table and data")
                df = pd.DataFrame([data])
                return df.to_sql(table, self.sqlite_conn, if_exists='append', index=False)
            
            elif operation.lower() == "update":
                # Execute parameterized UPDATE query
                if not data or not table or not where_clause:
                    raise ValueError("Update requires table, data, and where_clause")
                set_clause = ", ".join([f"{k} = ?" for k in data.keys()])
                query = f"UPDATE {table} SET {set_clause} WHERE {where_clause}"
                cursor = self.sqlite_conn.cursor()
                cursor.execute(query, list(data.values()))
                self.sqlite_conn.commit()
                return cursor.rowcount
            
            elif operation.lower() == "delete":
                # Execute DELETE query with WHERE clause
                if not table or not where_clause:
                    raise ValueError("Delete requires table and where_clause")
                query = f"DELETE FROM {table} WHERE {where_clause}"
                cursor = self.sqlite_conn.cursor()
                cursor.execute(query)
                self.sqlite_conn.commit()
                return cursor.rowcount
                
        except Exception as e:
            print(f"{operation.title()} failed:", e)
            return pd.DataFrame() if operation == "select" else None
    
    def close(self):
        """
        Close all database connections and clean up resources.
        Should be called when done with the database manager.
        """
        if self.engine:
            self.engine.dispose()  # Close all connections in the pool
        if self.sqlite_conn:
            self.sqlite_conn.close()

    def execute_procedure(self, proc_name, params=None, return_result=True):
        """
        Execute a stored procedure (MSSQL only).
        
        Args:
            proc_name (str): Name of the stored procedure
            params (dict): Parameters to pass to the procedure
            return_result (bool): Whether to return results as DataFrame
            
        Returns:
            pandas.DataFrame: If return_result=True and procedure returns data
            int: Number of affected rows if return_result=False
            None: If connection unavailable or procedure fails
        """
        if self.engine is None:
            return pd.DataFrame() if return_result else None

        try:
            with self.engine.connect() as conn:
                # Build parameter string for procedure call
                if params:
                    param_str = ", ".join([f":{key}" for key in params.keys()])
                    query = f"EXEC {proc_name} {param_str}"
                else:
                    query = f"EXEC {proc_name}"
                
                result = conn.execute(text(query), params or {})
                
                if return_result:
                    # Fetch and return results as DataFrame
                    rows = result.fetchall()
                    if rows:
                        columns = result.keys()
                        df = pd.DataFrame(rows, columns=columns) # type: ignore
                        print(f"Procedure {proc_name} returned {len(df)} records")
                        return df
                    else:
                        print(f"Procedure {proc_name} executed successfully (no results)")
                        return pd.DataFrame()
                else:
                    # Commit changes for non-returning procedures
                    conn.commit()
                    print(f"Procedure {proc_name} executed successfully")
                    return result.rowcount if hasattr(result, 'rowcount') else None
                    
        except Exception as e:
            print(f"Stored procedure {proc_name} failed:", e)
            return pd.DataFrame() if return_result else None

    def call_procedure(self, proc_name, params=None):
        """
        Alternative method using CALL syntax for stored procedures.
        Some databases prefer CALL over EXEC syntax.
        
        Args:
            proc_name (str): Name of the stored procedure
            params (dict): Parameters to pass to the procedure
            
        Returns:
            int: Number of affected rows
            None: If connection unavailable or procedure fails
        """
        if self.engine is None:
            return None

        try:
            with self.engine.connect() as conn:
                # Build CALL syntax query
                if params:
                    param_str = ", ".join([f":{key}" for key in params.keys()])
                    query = f"CALL {proc_name}({param_str})"
                else:
                    query = f"CALL {proc_name}()"
                
                result = conn.execute(text(query), params or {})
                conn.commit()
                print(f"Procedure {proc_name} called successfully")
                return result.rowcount if hasattr(result, 'rowcount') else None
                
        except Exception as e:
            print(f"Procedure call {proc_name} failed:", e)
            return None

# Example usage and testing code (commented out for production)
# if __name__ == "__main__":
#     # Initialize database manager
#     db = DatabaseManager()
    
#     # Check connection status
#     if db.engine is None:
#         print("Database connection failed")
#         exit(1)
    
#     # Example operations
#     df_users = db.execute("select", "users")
#     if df_users.empty:
#         print("No data returned.")
#     else:
#         print(df_users)
    
#     # CRUD operations examples
#     db.execute("select", "users", where_clause="user_id = '123'")
#     db.execute("insert", "users", {"user_name": "John", "user_email": "<EMAIL>"})
#     db.execute("update", "users", {"user_status": "inactive"}, "user_id = '123'")
#     db.execute("delete", "users", where_clause="user_id = '123'")
    
#     # Stored procedure examples
#     result_df = db.execute_procedure("GetUsersByRole", {"role_id": "admin"})
#     users_df = db.execute_procedure("GetAllActiveUsers")
#     db.execute_procedure("UpdateUserStatus", {"user_id": "123", "status": "inactive"}, return_result=False)
#     db.call_procedure("RefreshUserCache")
    
#     # Clean up
#     db.close()

# Database type examples:
# MSSQL (default): db_mssql = DatabaseManager()
# SQLite: db_sqlite = DatabaseManager(db_type="sqlite", sqlite_path="my_database.db")
